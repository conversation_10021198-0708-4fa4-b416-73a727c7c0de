"""
测试去掉buffer_days后的简洁逻辑
验证dropna是否能有效处理NaN问题
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TestNoBuffer:
    def __init__(self, lookback_days=60, predict_days=5):
        self.lookback_days = lookback_days
        self.predict_days = predict_days
        self.base_date = datetime(1990, 1, 1)
    
    def create_test_data(self, n_days=1200):
        """创建测试数据"""
        start_date = datetime(2020, 1, 1)
        dates = [start_date + timedelta(days=i) for i in range(n_days)]
        
        np.random.seed(42)
        base_price = 100
        returns = np.random.normal(0.0005, 0.02, n_days)
        
        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            daily_vol = abs(np.random.normal(0, 0.015))
            high = close * (1 + daily_vol * 0.6)
            low = close * (1 - daily_vol * 0.6)
            open_price = close * (1 + np.random.normal(0, 0.005))
            volume = np.random.randint(1000000, 10000000)
            amount = volume * close
            
            data.append({
                'datetime': int(date.strftime('%Y%m%d')),
                'date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume,
                'amount': amount
            })
        
        return pd.DataFrame(data)
    
    def split_data_simple(self, df):
        """简洁的数据分割，无缓冲区"""
        df_sorted = df.sort_values('date').reset_index(drop=True)
        total_days = len(df_sorted)
        
        # 简单按天数分割
        test_days = 365
        val_days = 365
        
        # 从后往前计算索引
        test_start_idx = total_days - test_days
        val_start_idx = test_start_idx - val_days
        
        # 确保索引有效
        val_start_idx = max(0, val_start_idx)
        test_start_idx = max(val_start_idx + val_days, test_start_idx)
        
        # 分割数据
        train_df = df_sorted.iloc[:val_start_idx].copy()
        val_df = df_sorted.iloc[val_start_idx:test_start_idx].copy()
        test_df = df_sorted.iloc[test_start_idx:].copy()
        
        return train_df, val_df, test_df
    
    def add_date_features(self, df, code='TEST001'):
        """添加日期特征"""
        data = df.copy()
        data['date_num'] = (data['date'] - self.base_date).dt.days
        data['id'] = data.apply(lambda row: f"{code}_{row['date_num']}", axis=1)
        return data
    
    def calculate_technical_features(self, df):
        """计算技术指标"""
        data = df.copy()
        
        # 基础特征
        data['returns'] = data['close'].pct_change()
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))
        
        # 移动平均
        for window in [5, 10, 20]:
            data[f'ma_{window}'] = data['close'].rolling(window).mean()
            data[f'ma_ratio_{window}'] = data['close'] / data[f'ma_{window}']
        
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量特征
        data['volume_ma'] = data['volume'].rolling(20).mean()
        data['volume_ratio'] = data['volume'] / data['volume_ma']
        data['amount_ratio'] = data['amount'].pct_change()
        
        # 波动率特征
        data['volatility'] = data['returns'].rolling(20).std()
        data['high_low_ratio'] = (data['high'] - data['low']) / data['close']
        
        return data
    
    def calculate_labels(self, df):
        """计算标签"""
        data = df.copy()
        
        # 未来5日收益率
        data['future_return'] = (data['close'].shift(-self.predict_days) / data['close'] - 1)
        
        # 简化的效率比率
        future_close = data['close'].shift(-self.predict_days)
        net_change = abs(future_close - data['close'])
        total_change = pd.Series(0.0, index=data.index)
        for i in range(1, self.predict_days + 1):
            total_change += abs(data['close'].shift(-i) - data['close'].shift(-i+1))
        data['efficiency_ratio'] = net_change / (total_change + 1e-8)
        
        # 简化的最大上涨
        future_highs = []
        for i in range(1, self.predict_days + 1):
            future_highs.append(data['high'].shift(-i))
        future_high_df = pd.concat(future_highs, axis=1)
        max_future_high = future_high_df.max(axis=1)
        data['max_upside'] = (max_future_high / data['close'] - 1)
        
        # 简化的最大回撤
        future_prices = []
        for i in range(1, self.predict_days + 1):
            future_prices.append(data['close'].shift(-i))
        future_df = pd.concat(future_prices, axis=1)
        running_max = future_df.expanding(axis=1).max()
        drawdowns = (future_df - running_max) / running_max
        data['max_drawdown'] = drawdowns.min(axis=1)
        
        return data
    
    def prepare_features(self, df):
        """准备特征"""
        feature_cols = [
            'open', 'high', 'low', 'close', 'volume', 'amount', 'date_num',
            'returns', 'log_returns', 'ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20',
            'rsi', 'volume_ratio', 'amount_ratio', 'volatility', 'high_low_ratio'
        ]
        return df[feature_cols].values
    
    def create_sequences(self, features, labels, ids):
        """创建序列"""
        slice_indices = []
        sequence_ids = []
        sequence_labels = []
        
        for i in range(self.lookback_days, len(features) - self.predict_days):
            slice_indices.append((i - self.lookback_days, i))
            sequence_ids.append(ids[i-1])
            sequence_labels.append(labels[i-1])
            
        return slice_indices, sequence_ids, sequence_labels
    
    def test_no_buffer_logic(self):
        """测试无缓冲区的简洁逻辑"""
        print("=== 测试无缓冲区的简洁逻辑 ===")
        
        # 1. 创建原始数据
        raw_df = self.create_test_data(1200)
        print(f"原始数据: {len(raw_df)} 天")
        
        # 2. 简洁分割
        train_raw, val_raw, test_raw = self.split_data_simple(raw_df)
        
        print(f"\n简洁分割结果:")
        print(f"训练集: {len(train_raw)} 天 ({train_raw['date'].min().date()} 到 {train_raw['date'].max().date()})")
        print(f"验证集: {len(val_raw)} 天 ({val_raw['date'].min().date()} 到 {val_raw['date'].max().date()})")
        print(f"测试集: {len(test_raw)} 天 ({test_raw['date'].min().date()} 到 {test_raw['date'].max().date()})")
        
        # 检查时间连续性
        val_gap = (val_raw['date'].min() - train_raw['date'].max()).days
        test_gap = (test_raw['date'].min() - val_raw['date'].max()).days
        print(f"时间间隔: 训练→验证 {val_gap}天, 验证→测试 {test_gap}天")
        
        # 3. 分别处理每个分割
        datasets = {'train': train_raw, 'val': val_raw, 'test': test_raw}
        
        for split_name, raw_data in datasets.items():
            print(f"\n=== 处理 {split_name} 数据集 ===")
            
            # 添加日期特征
            data_with_dates = self.add_date_features(raw_data)
            print(f"原始数据: {len(data_with_dates)} 天")
            
            # 计算技术指标
            data_with_features = self.calculate_technical_features(data_with_dates)
            
            # 检查NaN情况
            nan_counts = data_with_features.isnull().sum()
            print(f"NaN统计:")
            print(f"  MA5: {nan_counts['ma_5']} 个NaN")
            print(f"  MA20: {nan_counts['ma_20']} 个NaN") 
            print(f"  RSI: {nan_counts['rsi']} 个NaN")
            print(f"  Volume MA: {nan_counts['volume_ma']} 个NaN")
            
            # 计算标签
            data_with_labels = self.calculate_labels(data_with_features)
            
            # 关键步骤：dropna清理
            clean_data = data_with_labels.dropna()
            lost_days = len(data_with_labels) - len(clean_data)
            print(f"dropna清理: 损失 {lost_days} 天，剩余 {len(clean_data)} 天")
            
            if len(clean_data) < self.lookback_days + self.predict_days:
                print(f"  ⚠️ 清理后数据不足，无法创建序列")
                continue
            
            # 准备特征和标签
            features = self.prepare_features(clean_data)
            labels = clean_data[['future_return', 'efficiency_ratio', 'max_upside', 'max_drawdown']].values
            ids = clean_data['id'].tolist()
            
            # 创建序列
            slice_indices, sequence_ids, sequence_labels = self.create_sequences(features, labels, ids)
            
            print(f"生成序列: {len(slice_indices)} 个")
            
            # 验证序列
            if len(slice_indices) > 0:
                start_idx, end_idx = slice_indices[0]
                feat_start_date = clean_data.iloc[start_idx]['date']
                feat_end_date = clean_data.iloc[end_idx-1]['date']
                print(f"首个序列: 特征 {feat_start_date.date()} 到 {feat_end_date.date()}")
                print(f"✅ 逻辑简洁，dropna有效处理了NaN")
        
        print(f"\n🎉 无缓冲区逻辑测试完成！")
        print(f"优点: 逻辑简洁，代码更清晰")
        print(f"代价: 每个分割损失一些前期数据（被dropna清理）")


def main():
    """运行测试"""
    print("开始测试无缓冲区的简洁逻辑...")
    
    tester = TestNoBuffer()
    tester.test_no_buffer_logic()


if __name__ == "__main__":
    main()
