"""
测试更简洁的数据生成管道
先分割原始数据，再分别计算特征指标
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TestCleanerPipeline:
    def __init__(self, lookback_days=60, predict_days=5, test_years=2):
        self.lookback_days = lookback_days
        self.predict_days = predict_days
        self.test_years = test_years
        self.base_date = datetime(1990, 1, 1)
    
    def create_raw_data(self, n_days=1200):
        """创建原始OHLCV数据（不含任何衍生特征）"""
        start_date = datetime(2020, 1, 1)
        dates = [start_date + timedelta(days=i) for i in range(n_days)]
        
        np.random.seed(42)
        base_price = 100
        returns = np.random.normal(0.0005, 0.02, n_days)
        
        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            daily_vol = abs(np.random.normal(0, 0.015))
            high = close * (1 + daily_vol * 0.6)
            low = close * (1 - daily_vol * 0.6)
            open_price = close * (1 + np.random.normal(0, 0.005))
            volume = np.random.randint(1000000, 10000000)
            amount = volume * close
            
            data.append({
                'datetime': int(date.strftime('%Y%m%d')),
                'date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume,
                'amount': amount
            })
        
        return pd.DataFrame(data)
    
    def split_raw_data(self, df):
        """先分割原始数据"""
        df_sorted = df.sort_values('date').reset_index(drop=True)
        total_days = len(df_sorted)
        
        # 按天数分割
        test_days = 365
        val_days = 365
        buffer_days = self.lookback_days
        
        # 从后往前计算索引
        test_start_idx = total_days - test_days
        val_end_idx = test_start_idx - buffer_days
        val_start_idx = val_end_idx - val_days
        train_end_idx = val_start_idx - buffer_days
        
        # 确保索引有效
        train_end_idx = max(0, train_end_idx)
        val_start_idx = max(train_end_idx + buffer_days, val_start_idx)
        val_end_idx = max(val_start_idx, val_end_idx)
        test_start_idx = max(val_end_idx + buffer_days, test_start_idx)
        
        # 分割原始数据
        train_raw = df_sorted.iloc[:train_end_idx].copy()
        val_raw = df_sorted.iloc[val_start_idx:val_end_idx].copy()
        test_raw = df_sorted.iloc[test_start_idx:].copy()
        
        return train_raw, val_raw, test_raw
    
    def add_date_features(self, df, code='TEST001'):
        """添加日期特征"""
        data = df.copy()
        data['date_num'] = (data['date'] - self.base_date).dt.days
        data['id'] = data.apply(lambda row: f"{code}_{row['date_num']}", axis=1)
        return data
    
    def calculate_technical_features(self, df):
        """计算技术指标特征"""
        data = df.copy()
        
        # 基础特征
        data['returns'] = data['close'].pct_change()
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))
        
        # 移动平均
        for window in [5, 10, 20]:
            data[f'ma_{window}'] = data['close'].rolling(window).mean()
            data[f'ma_ratio_{window}'] = data['close'] / data[f'ma_{window}']
        
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量特征
        data['volume_ma'] = data['volume'].rolling(20).mean()
        data['volume_ratio'] = data['volume'] / data['volume_ma']
        data['amount_ratio'] = data['amount'].pct_change()
        
        # 波动率特征
        data['volatility'] = data['returns'].rolling(20).std()
        data['high_low_ratio'] = (data['high'] - data['low']) / data['close']
        
        return data
    
    def calculate_labels(self, df):
        """计算预测标签"""
        data = df.copy()
        
        # 未来5日收益率
        data['future_return'] = (data['close'].shift(-self.predict_days) / data['close'] - 1)
        
        # 效率比率
        future_close = data['close'].shift(-self.predict_days)
        net_change = abs(future_close - data['close'])
        total_change = pd.Series(0.0, index=data.index)
        for i in range(1, self.predict_days + 1):
            total_change += abs(data['close'].shift(-i) - data['close'].shift(-i+1))
        data['efficiency_ratio'] = net_change / (total_change + 1e-8)
        
        # 未来5日最大上涨
        future_highs = []
        for i in range(1, self.predict_days + 1):
            future_highs.append(data['high'].shift(-i))
        future_high_df = pd.concat(future_highs, axis=1)
        max_future_high = future_high_df.max(axis=1)
        data['max_upside'] = (max_future_high / data['close'] - 1)
        
        # 未来5日最大回撤
        future_prices = []
        for i in range(1, self.predict_days + 1):
            future_prices.append(data['close'].shift(-i))
        future_df = pd.concat(future_prices, axis=1)
        running_max = future_df.expanding(axis=1).max()
        drawdowns = (future_df - running_max) / running_max
        data['max_drawdown'] = drawdowns.min(axis=1)
        
        return data
    
    def prepare_features(self, df):
        """准备最终特征"""
        feature_cols = [
            # OHLCVA基础特征
            'open', 'high', 'low', 'close', 'volume', 'amount',
            # 日期特征
            'date_num',
            # 衍生技术特征
            'returns', 'log_returns', 'ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20',
            'rsi', 'volume_ratio', 'amount_ratio', 'volatility', 'high_low_ratio'
        ]
        return df[feature_cols].values
    
    def create_sequences(self, features, labels, ids):
        """创建序列"""
        slice_indices = []
        sequence_ids = []
        sequence_labels = []
        
        for i in range(self.lookback_days, len(features) - self.predict_days):
            slice_indices.append((i - self.lookback_days, i))
            sequence_ids.append(ids[i-1])  # 最近一天的ID
            sequence_labels.append(labels[i-1])  # 最近一天对应的未来标签
            
        return slice_indices, sequence_ids, sequence_labels
    
    def test_cleaner_pipeline(self):
        """测试更简洁的管道"""
        print("=== 测试更简洁的数据生成管道 ===")
        
        # 1. 创建原始数据
        raw_df = self.create_raw_data(1200)
        print(f"原始数据: {len(raw_df)} 天")
        print(f"原始数据列: {list(raw_df.columns)}")
        
        # 2. 先分割原始数据
        train_raw, val_raw, test_raw = self.split_raw_data(raw_df)
        
        print(f"\n原始数据分割:")
        print(f"训练集原始数据: {len(train_raw)} 天 ({train_raw['date'].min().date()} 到 {train_raw['date'].max().date()})")
        print(f"验证集原始数据: {len(val_raw)} 天 ({val_raw['date'].min().date()} 到 {val_raw['date'].max().date()})")
        print(f"测试集原始数据: {len(test_raw)} 天 ({test_raw['date'].min().date()} 到 {test_raw['date'].max().date()})")
        
        # 检查缓冲区
        train_to_val_gap = (val_raw['date'].min() - train_raw['date'].max()).days if len(train_raw) > 0 and len(val_raw) > 0 else 0
        val_to_test_gap = (test_raw['date'].min() - val_raw['date'].max()).days if len(val_raw) > 0 and len(test_raw) > 0 else 0
        
        print(f"缓冲区: 训练→验证 {train_to_val_gap}天, 验证→测试 {val_to_test_gap}天")
        buffer_ok = train_to_val_gap >= self.lookback_days and val_to_test_gap >= self.lookback_days
        print(f"缓冲区检查: {'✅ 通过' if buffer_ok else '❌ 失败'}")
        
        # 3. 分别处理每个分割
        datasets = {'train': train_raw, 'val': val_raw, 'test': test_raw}
        train_norm_stats = None
        
        for split_name, raw_data in datasets.items():
            if len(raw_data) < self.lookback_days + self.predict_days:
                print(f"\n{split_name}: 数据不足，跳过")
                continue
            
            print(f"\n=== 处理 {split_name} 数据集 ===")
            
            # 添加日期特征
            data_with_dates = self.add_date_features(raw_data)
            
            # 计算技术指标
            data_with_features = self.calculate_technical_features(data_with_dates)
            
            # 计算标签
            data_with_labels = self.calculate_labels(data_with_features)
            
            # 清理NaN
            clean_data = data_with_labels.dropna()
            
            print(f"处理后数据: {len(clean_data)} 天")
            print(f"特征列数: {len([col for col in clean_data.columns if col not in ['datetime', 'date', 'id']])} 个")
            
            # 准备特征和标签
            features = self.prepare_features(clean_data)
            labels = clean_data[['future_return', 'efficiency_ratio', 'max_upside', 'max_drawdown']].values
            ids = clean_data['id'].tolist()
            
            print(f"特征数组形状: {features.shape}")
            print(f"标签数组形状: {labels.shape}")
            
            # 计算标准化统计量（仅训练集）
            if split_name == 'train':
                from sklearn.preprocessing import StandardScaler
                scaler = StandardScaler()
                scaler.fit(features.reshape(-1, features.shape[-1]))
                train_norm_stats = {
                    'mean': scaler.mean_,
                    'scale': scaler.scale_,
                    'var': scaler.var_
                }
                print(f"标准化统计量已计算 (基于训练集)")
            
            # 创建序列
            slice_indices, sequence_ids, sequence_labels = self.create_sequences(features, labels, ids)
            
            print(f"生成序列: {len(slice_indices)} 个")
            
            # 验证前几个序列
            if len(slice_indices) > 0:
                for j in range(min(2, len(slice_indices))):
                    start_idx, end_idx = slice_indices[j]
                    feat_start_date = clean_data.iloc[start_idx]['date']
                    feat_end_date = clean_data.iloc[end_idx-1]['date']
                    label_date = clean_data.iloc[end_idx-1]['date']
                    
                    print(f"  序列{j}: 特征 {feat_start_date.date()} 到 {feat_end_date.date()}, 标签基准 {label_date.date()}")
                    
                    # 验证特征时间范围
                    split_start = clean_data['date'].min()
                    split_end = clean_data['date'].max()
                    
                    if feat_start_date >= split_start and feat_end_date <= split_end:
                        print(f"    ✅ 特征时间范围正确")
                    else:
                        print(f"    ❌ 特征时间范围错误")
        
        print(f"\n{'✅ 简洁管道测试通过！' if buffer_ok else '⚠️ 缓冲区问题'}")
        return buffer_ok


def main():
    """运行测试"""
    print("开始测试更简洁的数据生成管道...")
    
    tester = TestCleanerPipeline()
    success = tester.test_cleaner_pipeline()
    
    if success:
        print("\n🎉 简洁管道测试通过！逻辑更清晰。")
    else:
        print("\n⚠️ 仍有问题需要解决。")


if __name__ == "__main__":
    main()
