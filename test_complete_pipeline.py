"""
完整的数据生成管道测试
验证修复后的数据分割、标准化和序列创建逻辑
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

# 模拟数据生成器的完整逻辑
class TestQuantDataGenerator:
    def __init__(self, lookback_days=60, predict_days=5, test_years=2):
        self.lookback_days = lookback_days
        self.predict_days = predict_days
        self.test_years = test_years
        self.base_date = datetime(1990, 1, 1)
        
        # 创建测试输出目录
        os.makedirs('test_data/train', exist_ok=True)
        os.makedirs('test_data/val', exist_ok=True)
        os.makedirs('test_data/test', exist_ok=True)
    
    def create_realistic_data(self, n_days=1500):
        """创建更真实的股票数据（约4年）"""
        start_date = datetime(2020, 1, 1)
        dates = [start_date + timedelta(days=i) for i in range(n_days)]
        
        # 模拟股价随机游走
        np.random.seed(42)
        base_price = 100
        returns = np.random.normal(0.0005, 0.02, n_days)  # 日收益率
        
        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # 生成OHLC数据
            daily_vol = abs(np.random.normal(0, 0.015))
            high = close * (1 + daily_vol * 0.6)
            low = close * (1 - daily_vol * 0.6)
            open_price = close * (1 + np.random.normal(0, 0.005))
            
            volume = np.random.randint(1000000, 10000000)
            amount = volume * close
            
            data.append({
                'datetime': int(date.strftime('%Y%m%d')),
                'date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume,
                'amount': amount
            })
        
        return pd.DataFrame(data)
    
    def add_date_features(self, df, code='TEST001'):
        """添加日期特征"""
        data = df.copy()
        data['date_num'] = (data['date'] - self.base_date).dt.days
        data['id'] = data.apply(lambda row: f"{code}_{row['date_num']}", axis=1)
        return data
    
    def calculate_simple_features(self, df):
        """计算简化的技术特征"""
        data = df.copy()
        
        # 基础特征
        data['returns'] = data['close'].pct_change()
        data['ma_5'] = data['close'].rolling(5).mean()
        data['ma_20'] = data['close'].rolling(20).mean()
        data['ma_ratio_5'] = data['close'] / data['ma_5']
        data['ma_ratio_20'] = data['close'] / data['ma_20']
        data['volume_ratio'] = data['volume'] / data['volume'].rolling(20).mean()
        
        return data
    
    def calculate_labels(self, df):
        """计算预测标签"""
        data = df.copy()
        data['future_return'] = (data['close'].shift(-self.predict_days) / data['close'] - 1)
        return data
    
    def split_data_by_time(self, df):
        """修复后的数据分割逻辑"""
        df_sorted = df.sort_values('date')

        max_date = df_sorted['date'].max()
        test_start_date = max_date - pd.DateOffset(years=1)
        val_end_date = test_start_date - pd.DateOffset(days=self.lookback_days + 1)
        val_start_date = max_date - pd.DateOffset(years=self.test_years)
        train_end_date = val_start_date - pd.DateOffset(days=self.lookback_days + 1)

        train_df = df_sorted[df_sorted['date'] <= train_end_date]
        val_df = df_sorted[(df_sorted['date'] > train_end_date) & (df_sorted['date'] <= val_end_date)]
        test_df = df_sorted[df_sorted['date'] >= test_start_date]

        return train_df, val_df, test_df
    
    def prepare_features(self, df):
        """准备特征数据"""
        feature_cols = ['open', 'high', 'low', 'close', 'volume', 'amount', 'date_num',
                       'returns', 'ma_ratio_5', 'ma_ratio_20', 'volume_ratio']
        return df[feature_cols].values
    
    def calculate_normalization_stats(self, train_features):
        """计算标准化统计量"""
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        scaler.fit(train_features.reshape(-1, train_features.shape[-1]))
        
        return {
            'mean': scaler.mean_,
            'scale': scaler.scale_,
            'var': scaler.var_
        }
    
    def test_complete_pipeline(self):
        """测试完整的数据生成管道"""
        print("=== 完整管道测试 ===")
        
        # 创建数据
        df = self.create_realistic_data(1500)
        df = self.add_date_features(df)
        df = self.calculate_simple_features(df)
        df = self.calculate_labels(df)
        df = df.dropna()
        
        print(f"原始数据: {len(df)} 天")
        print(f"日期范围: {df['date'].min().date()} 到 {df['date'].max().date()}")
        
        # 分割数据
        train_df, val_df, test_df = self.split_data_by_time(df)
        
        print(f"\n数据分割结果:")
        print(f"训练集: {len(train_df)} 天 ({train_df['date'].min().date()} 到 {train_df['date'].max().date()})")
        print(f"验证集: {len(val_df)} 天 ({val_df['date'].min().date()} 到 {val_df['date'].max().date()})")
        print(f"测试集: {len(test_df)} 天 ({test_df['date'].min().date()} 到 {test_df['date'].max().date()})")
        
        # 检查缓冲区
        train_to_val_gap = (val_df['date'].min() - train_df['date'].max()).days
        val_to_test_gap = (test_df['date'].min() - val_df['date'].max()).days
        
        print(f"\n缓冲区检查:")
        print(f"训练集到验证集间隔: {train_to_val_gap} 天 (需要 >= {self.lookback_days})")
        print(f"验证集到测试集间隔: {val_to_test_gap} 天 (需要 >= {self.lookback_days})")
        
        buffer_ok = train_to_val_gap >= self.lookback_days and val_to_test_gap >= self.lookback_days
        print(f"缓冲区检查: {'✅ 通过' if buffer_ok else '❌ 失败'}")
        
        # 准备特征
        all_features = self.prepare_features(df)
        all_labels = df[['future_return']].values
        all_ids = df['id'].tolist()
        
        # 计算标准化统计量（仅基于训练集）
        train_features = self.prepare_features(train_df)
        norm_stats = self.calculate_normalization_stats(train_features)
        
        print(f"\n标准化统计量 (基于训练集):")
        print(f"特征均值范围: {norm_stats['mean'].min():.4f} 到 {norm_stats['mean'].max():.4f}")
        print(f"特征标准差范围: {norm_stats['scale'].min():.4f} 到 {norm_stats['scale'].max():.4f}")
        
        # 测试序列创建
        datasets = {'train': train_df, 'val': val_df, 'test': test_df}
        
        print(f"\n序列创建测试:")
        for split_name, split_df in datasets.items():
            if len(split_df) < self.lookback_days + self.predict_days:
                print(f"{split_name}: 数据不足，跳过")
                continue
            
            # 获取分割的索引范围
            start_idx = df.index.get_loc(split_df.index[0])
            end_idx = df.index.get_loc(split_df.index[-1]) + 1
            
            # 创建序列
            slice_indices, sequence_ids, sequence_labels = [], [], []
            
            for i in range(start_idx + self.lookback_days, end_idx - self.predict_days):
                if i < len(all_features):
                    slice_indices.append((i - self.lookback_days, i))
                    sequence_ids.append(all_ids[i-1])
                    sequence_labels.append(all_labels[i-1])
            
            print(f"{split_name}: {len(slice_indices)} 个序列")
            
            # 验证前几个序列的时间范围
            if len(slice_indices) > 0:
                for j in range(min(2, len(slice_indices))):
                    start_feat_idx, end_feat_idx = slice_indices[j]
                    feat_start_date = df.iloc[start_feat_idx]['date']
                    feat_end_date = df.iloc[end_feat_idx-1]['date']
                    label_date = df.iloc[end_feat_idx-1]['date']
                    
                    print(f"  序列{j}: 特征 {feat_start_date.date()} 到 {feat_end_date.date()}, 标签基准 {label_date.date()}")
                    
                    # 检查是否有数据泄露
                    if split_name == 'train' and feat_end_date >= val_df['date'].min():
                        print(f"    ⚠️ 训练集特征泄露到验证集！")
                    if split_name == 'val' and feat_end_date >= test_df['date'].min():
                        print(f"    ⚠️ 验证集特征泄露到测试集！")
        
        print(f"\n✅ 完整管道测试完成")
        return buffer_ok


def main():
    """运行完整测试"""
    print("开始完整数据生成管道测试...")
    
    tester = TestQuantDataGenerator()
    success = tester.test_complete_pipeline()
    
    if success:
        print("\n🎉 所有测试通过！数据生成逻辑正确。")
    else:
        print("\n⚠️ 发现问题，需要进一步修复。")


if __name__ == "__main__":
    main()
