"""
测试修复后的数据生成管道
验证先分割再计算特征的新逻辑
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TestFixedPipeline:
    def __init__(self, lookback_days=60, predict_days=5, test_years=2):
        self.lookback_days = lookback_days
        self.predict_days = predict_days
        self.test_years = test_years
        self.base_date = datetime(1990, 1, 1)
    
    def create_test_data(self, n_days=1200):
        """创建测试数据"""
        start_date = datetime(2020, 1, 1)
        dates = [start_date + timedelta(days=i) for i in range(n_days)]
        
        np.random.seed(42)
        base_price = 100
        returns = np.random.normal(0.0005, 0.02, n_days)
        
        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            daily_vol = abs(np.random.normal(0, 0.015))
            high = close * (1 + daily_vol * 0.6)
            low = close * (1 - daily_vol * 0.6)
            open_price = close * (1 + np.random.normal(0, 0.005))
            volume = np.random.randint(1000000, 10000000)
            amount = volume * close
            
            data.append({
                'datetime': int(date.strftime('%Y%m%d')),
                'date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume,
                'amount': amount
            })
        
        return pd.DataFrame(data)
    
    def add_date_features(self, df, code='TEST001'):
        """添加日期特征"""
        data = df.copy()
        data['date_num'] = (data['date'] - self.base_date).dt.days
        data['id'] = data.apply(lambda row: f"{code}_{row['date_num']}", axis=1)
        return data
    
    def calculate_simple_features(self, df):
        """计算简化特征"""
        data = df.copy()
        data['returns'] = data['close'].pct_change()
        data['ma_5'] = data['close'].rolling(5).mean()
        data['ma_ratio_5'] = data['close'] / data['ma_5']
        data['volume_ratio'] = data['volume'] / data['volume'].rolling(10).mean()
        return data
    
    def calculate_labels(self, df):
        """计算标签"""
        data = df.copy()
        data['future_return'] = (data['close'].shift(-self.predict_days) / data['close'] - 1)
        
        # 简化的效率比率
        future_close = data['close'].shift(-self.predict_days)
        net_change = abs(future_close - data['close'])
        total_change = pd.Series(0.0, index=data.index)
        for i in range(1, self.predict_days + 1):
            total_change += abs(data['close'].shift(-i) - data['close'].shift(-i+1))
        data['efficiency_ratio'] = net_change / (total_change + 1e-8)
        
        # 简化的最大上涨
        future_highs = []
        for i in range(1, self.predict_days + 1):
            future_highs.append(data['high'].shift(-i))
        future_high_df = pd.concat(future_highs, axis=1)
        max_future_high = future_high_df.max(axis=1)
        data['max_upside'] = (max_future_high / data['close'] - 1)
        
        # 简化的最大回撤
        future_prices = []
        for i in range(1, self.predict_days + 1):
            future_prices.append(data['close'].shift(-i))
        future_df = pd.concat(future_prices, axis=1)
        running_max = future_df.expanding(axis=1).max()
        drawdowns = (future_df - running_max) / running_max
        data['max_drawdown'] = drawdowns.min(axis=1)
        
        return data
    
    def split_data_by_time(self, df):
        """按索引位置精确分割数据"""
        df_sorted = df.sort_values('date').reset_index(drop=True)
        total_days = len(df_sorted)
        
        # 按天数分割
        test_days = 365
        val_days = 365
        buffer_days = self.lookback_days
        
        # 从后往前计算索引
        test_start_idx = total_days - test_days
        val_end_idx = test_start_idx - buffer_days
        val_start_idx = val_end_idx - val_days
        train_end_idx = val_start_idx - buffer_days
        
        # 确保索引有效
        train_end_idx = max(0, train_end_idx)
        val_start_idx = max(train_end_idx + buffer_days, val_start_idx)
        val_end_idx = max(val_start_idx, val_end_idx)
        test_start_idx = max(val_end_idx + buffer_days, test_start_idx)
        
        # 分割数据
        train_df = df_sorted.iloc[:train_end_idx].copy()
        val_df = df_sorted.iloc[val_start_idx:val_end_idx].copy()
        test_df = df_sorted.iloc[test_start_idx:].copy()
        
        return train_df, val_df, test_df
    
    def prepare_features(self, df):
        """准备特征"""
        feature_cols = ['open', 'high', 'low', 'close', 'volume', 'amount', 'date_num',
                       'returns', 'ma_ratio_5', 'volume_ratio']
        return df[feature_cols].values
    
    def create_sequences_with_indices(self, features, labels, ids):
        """创建序列"""
        slice_indices = []
        sequence_ids = []
        sequence_labels = []
        
        for i in range(self.lookback_days, len(features) - self.predict_days):
            slice_indices.append((i - self.lookback_days, i))
            sequence_ids.append(ids[i-1])
            sequence_labels.append(labels[i-1])
            
        return slice_indices, sequence_ids, sequence_labels
    
    def test_new_pipeline(self):
        """测试新的管道逻辑"""
        print("=== 测试修复后的管道逻辑 ===")
        
        # 创建数据
        df = self.create_test_data(1200)
        df = self.add_date_features(df)
        df = self.calculate_simple_features(df)
        df = self.calculate_labels(df)
        df = df.dropna()
        
        print(f"原始数据: {len(df)} 天")
        print(f"日期范围: {df['date'].min().date()} 到 {df['date'].max().date()}")
        
        # 分割数据
        train_df, val_df, test_df = self.split_data_by_time(df)
        
        print(f"\n数据分割结果:")
        print(f"训练集: {len(train_df)} 天 ({train_df['date'].min().date() if len(train_df) > 0 else 'N/A'} 到 {train_df['date'].max().date() if len(train_df) > 0 else 'N/A'})")
        print(f"验证集: {len(val_df)} 天 ({val_df['date'].min().date() if len(val_df) > 0 else 'N/A'} 到 {val_df['date'].max().date() if len(val_df) > 0 else 'N/A'})")
        print(f"测试集: {len(test_df)} 天 ({test_df['date'].min().date() if len(test_df) > 0 else 'N/A'} 到 {test_df['date'].max().date() if len(test_df) > 0 else 'N/A'})")
        
        # 检查缓冲区
        if len(val_df) > 0 and len(test_df) > 0:
            val_to_test_gap = (test_df['date'].min() - val_df['date'].max()).days
        else:
            val_to_test_gap = 0
            
        if len(train_df) > 0 and len(val_df) > 0:
            train_to_val_gap = (val_df['date'].min() - train_df['date'].max()).days
        else:
            train_to_val_gap = 0
        
        print(f"\n缓冲区检查:")
        print(f"训练集到验证集间隔: {train_to_val_gap} 天 (需要 >= {self.lookback_days})")
        print(f"验证集到测试集间隔: {val_to_test_gap} 天 (需要 >= {self.lookback_days})")
        
        buffer_ok = train_to_val_gap >= self.lookback_days and val_to_test_gap >= self.lookback_days
        print(f"缓冲区检查: {'✅ 通过' if buffer_ok else '❌ 失败'}")
        
        # 测试分别计算特征的逻辑
        print(f"\n=== 测试分别计算特征逻辑 ===")
        
        datasets = {'train': train_df, 'val': val_df, 'test': test_df}
        
        for split_name, split_df in datasets.items():
            if len(split_df) < self.lookback_days + self.predict_days:
                print(f"{split_name}: 数据不足，跳过")
                continue
            
            # 为当前分割准备特征和标签
            split_features = self.prepare_features(split_df)
            split_labels = split_df[['future_return', 'efficiency_ratio', 'max_upside', 'max_drawdown']].values
            split_ids = split_df['id'].tolist()
            
            # 在当前分割内创建序列
            slice_indices, sequence_ids, sequence_labels = self.create_sequences_with_indices(
                split_features, split_labels, split_ids
            )
            
            print(f"\n{split_name} 数据集:")
            print(f"  分割数据: {len(split_df)} 天")
            print(f"  特征数组: {split_features.shape}")
            print(f"  生成序列: {len(slice_indices)} 个")
            
            # 验证前几个序列
            if len(slice_indices) > 0:
                for j in range(min(2, len(slice_indices))):
                    start_idx, end_idx = slice_indices[j]
                    
                    # 获取特征时间范围（在当前分割内）
                    feat_start_date = split_df.iloc[start_idx]['date']
                    feat_end_date = split_df.iloc[end_idx-1]['date']
                    label_date = split_df.iloc[end_idx-1]['date']
                    
                    print(f"    序列{j}: 特征 {feat_start_date.date()} 到 {feat_end_date.date()}, 标签基准 {label_date.date()}")
                    
                    # 验证特征不会超出当前分割的边界
                    split_start_date = split_df['date'].min()
                    split_end_date = split_df['date'].max()
                    
                    if feat_start_date < split_start_date or feat_end_date > split_end_date:
                        print(f"      ⚠️ 特征超出分割边界！")
                    else:
                        print(f"      ✅ 特征在分割边界内")
        
        print(f"\n{'✅ 新管道逻辑测试通过！' if buffer_ok else '⚠️ 缓冲区仍有问题'}")
        return buffer_ok


def main():
    """运行测试"""
    print("开始测试修复后的数据生成管道...")
    
    tester = TestFixedPipeline()
    success = tester.test_new_pipeline()
    
    if success:
        print("\n🎉 所有测试通过！新的数据生成逻辑正确。")
    else:
        print("\n⚠️ 仍有问题需要解决。")


if __name__ == "__main__":
    main()
