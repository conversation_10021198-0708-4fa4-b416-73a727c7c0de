"""
测试 buffer_days 的必要性
验证技术指标计算是否需要缓冲区
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def create_test_data(n_days=200):
    """创建测试数据"""
    start_date = datetime(2020, 1, 1)
    dates = [start_date + timedelta(days=i) for i in range(n_days)]
    
    np.random.seed(42)
    base_price = 100
    returns = np.random.normal(0.001, 0.02, n_days)
    
    prices = [base_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        data.append({
            'date': date,
            'close': close,
            'volume': np.random.randint(1000000, 10000000)
        })
    
    return pd.DataFrame(data)

def calculate_technical_indicators(df):
    """计算技术指标"""
    data = df.copy()
    
    # 移动平均
    data['ma_5'] = data['close'].rolling(5).mean()
    data['ma_20'] = data['close'].rolling(20).mean()
    
    # RSI (需要14天)
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))
    
    # 成交量移动平均
    data['volume_ma'] = data['volume'].rolling(20).mean()
    
    return data

def test_without_buffer():
    """测试没有缓冲区的情况"""
    print("=== 测试：没有缓冲区的分割 ===")
    
    # 创建数据
    df = create_test_data(200)
    print(f"总数据: {len(df)} 天")
    
    # 简单分割（无缓冲区）
    train_size = 100
    val_size = 50
    test_size = 50
    
    train_df = df.iloc[:train_size].copy()
    val_df = df.iloc[train_size:train_size+val_size].copy()
    test_df = df.iloc[train_size+val_size:].copy()
    
    print(f"训练集: {len(train_df)} 天")
    print(f"验证集: {len(val_df)} 天") 
    print(f"测试集: {len(test_df)} 天")
    
    # 分别计算技术指标
    datasets = {'train': train_df, 'val': val_df, 'test': test_df}
    
    for name, data in datasets.items():
        print(f"\n{name} 数据集技术指标计算:")
        
        # 计算技术指标
        data_with_indicators = calculate_technical_indicators(data)
        
        # 检查NaN情况
        nan_counts = data_with_indicators.isnull().sum()
        print(f"  MA5 NaN数量: {nan_counts['ma_5']}")
        print(f"  MA20 NaN数量: {nan_counts['ma_20']}")
        print(f"  RSI NaN数量: {nan_counts['rsi']}")
        print(f"  Volume MA NaN数量: {nan_counts['volume_ma']}")
        
        # 清理后的有效数据
        clean_data = data_with_indicators.dropna()
        print(f"  清理后有效数据: {len(clean_data)} 天 (损失 {len(data) - len(clean_data)} 天)")
        
        if len(clean_data) < 60:  # 假设需要60天lookback
            print(f"  ⚠️ 有效数据不足60天，无法创建序列！")

def test_with_buffer():
    """测试有缓冲区的情况"""
    print("\n\n=== 测试：有缓冲区的分割 ===")
    
    # 创建数据
    df = create_test_data(200)
    print(f"总数据: {len(df)} 天")
    
    # 带缓冲区的分割
    buffer_days = 30  # 足够计算技术指标
    train_size = 100
    val_size = 50
    test_size = 50
    
    # 从后往前计算
    test_start = len(df) - test_size
    val_end = test_start - buffer_days
    val_start = val_end - val_size
    train_end = val_start - buffer_days
    
    train_df = df.iloc[:train_end].copy()
    val_df = df.iloc[val_start:val_end].copy()
    test_df = df.iloc[test_start:].copy()
    
    print(f"训练集: {len(train_df)} 天")
    print(f"验证集: {len(val_df)} 天")
    print(f"测试集: {len(test_df)} 天")
    print(f"缓冲区: {buffer_days} 天")
    
    # 分别计算技术指标
    datasets = {'train': train_df, 'val': val_df, 'test': test_df}
    
    for name, data in datasets.items():
        print(f"\n{name} 数据集技术指标计算:")
        
        # 计算技术指标
        data_with_indicators = calculate_technical_indicators(data)
        
        # 检查NaN情况
        nan_counts = data_with_indicators.isnull().sum()
        print(f"  MA5 NaN数量: {nan_counts['ma_5']}")
        print(f"  MA20 NaN数量: {nan_counts['ma_20']}")
        print(f"  RSI NaN数量: {nan_counts['rsi']}")
        print(f"  Volume MA NaN数量: {nan_counts['volume_ma']}")
        
        # 清理后的有效数据
        clean_data = data_with_indicators.dropna()
        print(f"  清理后有效数据: {len(clean_data)} 天 (损失 {len(data) - len(clean_data)} 天)")
        
        if len(clean_data) >= 60:  # 假设需要60天lookback
            print(f"  ✅ 有效数据足够创建序列")
        else:
            print(f"  ⚠️ 有效数据不足60天")

def test_optimal_buffer():
    """测试最优缓冲区大小"""
    print("\n\n=== 测试：最优缓冲区大小 ===")
    
    # 分析技术指标的最大回看需求
    indicators_lookback = {
        'MA5': 5,
        'MA20': 20,
        'RSI': 14,
        'Volume_MA': 20
    }
    
    max_indicator_lookback = max(indicators_lookback.values())
    print(f"技术指标最大回看需求: {max_indicator_lookback} 天")
    
    # 序列创建的回看需求
    sequence_lookback = 60
    print(f"序列创建回看需求: {sequence_lookback} 天")
    
    # 建议的缓冲区大小
    suggested_buffer = max(max_indicator_lookback, sequence_lookback)
    print(f"建议缓冲区大小: {suggested_buffer} 天")
    
    print(f"\n结论:")
    print(f"1. 技术指标计算需要历史数据，最多需要 {max_indicator_lookback} 天")
    print(f"2. 序列创建需要 {sequence_lookback} 天的特征")
    print(f"3. 缓冲区应该至少为 {suggested_buffer} 天")
    print(f"4. 当前使用 lookback_days ({sequence_lookback}) 作为缓冲区是合理的")

def main():
    """运行所有测试"""
    print("开始测试 buffer_days 的必要性...")
    
    test_without_buffer()
    test_with_buffer()
    test_optimal_buffer()
    
    print(f"\n🎯 结论: buffer_days 是必要的！")
    print(f"原因:")
    print(f"1. 技术指标计算需要历史数据")
    print(f"2. 没有缓冲区会导致验证集和测试集前面的数据无法计算技术指标")
    print(f"3. 这会减少可用的训练样本数量")
    print(f"4. 使用 lookback_days 作为缓冲区大小是合理的选择")

if __name__ == "__main__":
    main()
