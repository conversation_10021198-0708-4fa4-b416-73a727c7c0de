"""
测试数据生成中的数据泄露问题
验证训练/验证/测试数据的时间边界是否正确
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


def test_data_split_logic():
    """测试数据分割逻辑是否存在数据泄露"""
    print("=== 测试数据分割逻辑 ===")
    
    # 创建模拟数据
    dates = [datetime(2020, 1, 1) + timedelta(days=i) for i in range(1000)]  # 约3年数据
    
    df = pd.DataFrame({
        'datetime': [int(d.strftime('%Y%m%d')) for d in dates],
        'date': dates,
        'close': np.random.randn(1000).cumsum() + 100,
        'high': np.random.randn(1000).cumsum() + 102,
        'low': np.random.randn(1000).cumsum() + 98,
        'open': np.random.randn(1000).cumsum() + 100,
        'volume': np.random.randint(1000000, 10000000, 1000),
        'amount': np.random.randint(100000000, 1000000000, 1000)
    })
    
    # 模拟数据分割逻辑
    test_years = 2
    lookback_days = 60
    predict_days = 5
    
    df_sorted = df.sort_values('date')
    
    # 分割点
    cutoff_date = df_sorted['date'].max() - pd.DateOffset(years=test_years)
    val_test_cutoff = df_sorted['date'].max() - pd.DateOffset(years=1)
    
    train_df = df_sorted[df_sorted['date'] < cutoff_date]
    val_df = df_sorted[(df_sorted['date'] >= cutoff_date) & (df_sorted['date'] < val_test_cutoff)]
    test_df = df_sorted[df_sorted['date'] >= val_test_cutoff]
    
    print(f"总数据范围: {df_sorted['date'].min()} 到 {df_sorted['date'].max()}")
    print(f"训练集范围: {train_df['date'].min()} 到 {train_df['date'].max()}")
    print(f"验证集范围: {val_df['date'].min()} 到 {val_df['date'].max()}")
    print(f"测试集范围: {test_df['date'].min()} 到 {test_df['date'].max()}")
    
    # 检查当前的序列创建逻辑
    print(f"\n=== 检查序列创建逻辑 ===")
    
    # 模拟当前的逻辑
    all_features = np.random.randn(len(df), 10)  # 假设10个特征
    all_labels = np.random.randn(len(df), 4)     # 4个标签
    all_ids = [f"test_{i}" for i in range(len(df))]
    
    datasets = {
        'train': train_df,
        'val': val_df, 
        'test': test_df
    }
    
    for split_name, split_df in datasets.items():
        if len(split_df) < lookback_days + predict_days:
            print(f"{split_name}: 数据不足")
            continue
            
        # 当前的逻辑：使用全局索引
        start_idx = df.index.get_loc(split_df.index[0])
        end_idx = df.index.get_loc(split_df.index[-1]) + 1
        
        print(f"\n{split_name} 数据集:")
        print(f"  分割数据索引范围: {split_df.index[0]} 到 {split_df.index[-1]}")
        print(f"  全局索引范围: {start_idx} 到 {end_idx-1}")
        
        # 检查序列创建
        sample_sequences = []
        for i in range(start_idx + lookback_days, min(start_idx + lookback_days + 3, end_idx - predict_days)):
            if i < len(all_features):
                feature_range = (i - lookback_days, i)
                label_idx = i - 1
                
                # 检查特征时间范围
                feature_start_date = df.iloc[feature_range[0]]['date']
                feature_end_date = df.iloc[feature_range[1]-1]['date']
                label_date = df.iloc[label_idx]['date']
                
                sample_sequences.append({
                    'feature_range': feature_range,
                    'feature_dates': (feature_start_date, feature_end_date),
                    'label_idx': label_idx,
                    'label_date': label_date
                })
        
        # 检查是否存在数据泄露
        for seq in sample_sequences[:2]:  # 只检查前2个样本
            print(f"  样本特征时间: {seq['feature_dates'][0].date()} 到 {seq['feature_dates'][1].date()}")
            print(f"  样本标签时间: {seq['label_date'].date()}")
            
            # 检查特征是否包含未来数据
            if split_name == 'train':
                # 训练集的特征不应该包含验证集或测试集的数据
                if seq['feature_dates'][1] >= val_df['date'].min():
                    print(f"  ⚠️  数据泄露：训练集特征包含验证集时间的数据！")
                if seq['feature_dates'][1] >= test_df['date'].min():
                    print(f"  ⚠️  数据泄露：训练集特征包含测试集时间的数据！")
            elif split_name == 'val':
                # 验证集的特征不应该包含测试集的数据
                if seq['feature_dates'][1] >= test_df['date'].min():
                    print(f"  ⚠️  数据泄露：验证集特征包含测试集时间的数据！")


def test_correct_split_logic():
    """测试正确的数据分割逻辑"""
    print(f"\n\n=== 建议的正确分割逻辑 ===")
    
    # 创建模拟数据
    dates = [datetime(2020, 1, 1) + timedelta(days=i) for i in range(1000)]
    
    df = pd.DataFrame({
        'datetime': [int(d.strftime('%Y%m%d')) for d in dates],
        'date': dates,
        'close': np.random.randn(1000).cumsum() + 100,
    })
    
    test_years = 2
    lookback_days = 60
    predict_days = 5
    
    df_sorted = df.sort_values('date')
    
    # 正确的分割逻辑：考虑lookback_days的影响
    cutoff_date = df_sorted['date'].max() - pd.DateOffset(years=test_years)
    val_test_cutoff = df_sorted['date'].max() - pd.DateOffset(years=1)
    
    # 训练集：需要确保最后一个样本的特征不会泄露到验证集
    train_cutoff_with_buffer = cutoff_date - pd.DateOffset(days=lookback_days)
    train_df = df_sorted[df_sorted['date'] <= train_cutoff_with_buffer]
    
    # 验证集：需要确保最后一个样本的特征不会泄露到测试集
    val_cutoff_with_buffer = val_test_cutoff - pd.DateOffset(days=lookback_days)
    val_df = df_sorted[(df_sorted['date'] > train_cutoff_with_buffer) & 
                       (df_sorted['date'] <= val_cutoff_with_buffer)]
    
    # 测试集
    test_df = df_sorted[df_sorted['date'] > val_cutoff_with_buffer]
    
    print(f"修正后的分割:")
    print(f"训练集范围: {train_df['date'].min()} 到 {train_df['date'].max()}")
    print(f"验证集范围: {val_df['date'].min()} 到 {val_df['date'].max()}")
    print(f"测试集范围: {test_df['date'].min()} 到 {test_df['date'].max()}")
    
    # 验证缓冲区
    train_to_val_gap = (val_df['date'].min() - train_df['date'].max()).days
    val_to_test_gap = (test_df['date'].min() - val_df['date'].max()).days
    
    print(f"训练集到验证集间隔: {train_to_val_gap} 天")
    print(f"验证集到测试集间隔: {val_to_test_gap} 天")
    print(f"lookback_days: {lookback_days} 天")
    
    if train_to_val_gap >= lookback_days:
        print("✅ 训练集到验证集的缓冲区足够")
    else:
        print("❌ 训练集到验证集的缓冲区不足")
        
    if val_to_test_gap >= lookback_days:
        print("✅ 验证集到测试集的缓冲区足够")
    else:
        print("❌ 验证集到测试集的缓冲区不足")


if __name__ == "__main__":
    test_data_split_logic()
    test_correct_split_logic()
