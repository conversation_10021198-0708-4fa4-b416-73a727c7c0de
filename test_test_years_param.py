"""
测试 test_years 参数的正确使用
验证不同 test_years 值的数据分割效果
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TestYearsValidator:
    def __init__(self, lookback_days=60, predict_days=5):
        self.lookback_days = lookback_days
        self.predict_days = predict_days
    
    def create_test_data(self, n_days=1500):
        """创建测试数据（约4年）"""
        start_date = datetime(2020, 1, 1)
        dates = [start_date + timedelta(days=i) for i in range(n_days)]
        
        np.random.seed(42)
        base_price = 100
        returns = np.random.normal(0.0005, 0.02, n_days)
        
        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            data.append({
                'datetime': int(date.strftime('%Y%m%d')),
                'date': date,
                'open': close * (1 + np.random.normal(0, 0.005)),
                'high': close * (1 + abs(np.random.normal(0, 0.01))),
                'low': close * (1 - abs(np.random.normal(0, 0.01))),
                'close': close,
                'volume': np.random.randint(1000000, 10000000),
                'amount': np.random.randint(100000000, 1000000000)
            })
        
        return pd.DataFrame(data)
    
    def split_data_by_time(self, df, test_years):
        """模拟修复后的分割逻辑"""
        df_sorted = df.sort_values('date').reset_index(drop=True)
        total_days = len(df_sorted)
        
        # 使用配置的年数计算天数
        days_per_year = 365
        test_days = days_per_year * 1  # 最后1年用于测试
        val_days = days_per_year * 1   # 倒数第2年用于验证
        
        # 如果test_years>2，则测试集占1年，验证集占剩余年数
        if test_years > 2:
            val_days = days_per_year * (test_years - 1)
        
        # 从后往前计算索引
        test_start_idx = total_days - test_days
        val_start_idx = test_start_idx - val_days
        
        # 确保索引有效
        val_start_idx = max(0, val_start_idx)
        test_start_idx = max(val_start_idx + val_days, test_start_idx)
        
        # 分割数据
        train_df = df_sorted.iloc[:val_start_idx].copy()
        val_df = df_sorted.iloc[val_start_idx:test_start_idx].copy()
        test_df = df_sorted.iloc[test_start_idx:].copy()
        
        return train_df, val_df, test_df
    
    def test_different_test_years(self):
        """测试不同的test_years值"""
        print("=== 测试不同的 test_years 参数 ===")
        
        # 创建约4年的数据
        df = self.create_test_data(1500)
        print(f"总数据: {len(df)} 天 ({df['date'].min().date()} 到 {df['date'].max().date()})")
        
        # 测试不同的test_years值
        test_years_values = [1, 2, 3, 4]
        
        for test_years in test_years_values:
            print(f"\n--- test_years = {test_years} ---")
            
            try:
                train_df, val_df, test_df = self.split_data_by_time(df, test_years)
                
                print(f"训练集: {len(train_df)} 天 ({train_df['date'].min().date() if len(train_df) > 0 else 'N/A'} 到 {train_df['date'].max().date() if len(train_df) > 0 else 'N/A'})")
                print(f"验证集: {len(val_df)} 天 ({val_df['date'].min().date() if len(val_df) > 0 else 'N/A'} 到 {val_df['date'].max().date() if len(val_df) > 0 else 'N/A'})")
                print(f"测试集: {len(test_df)} 天 ({test_df['date'].min().date() if len(test_df) > 0 else 'N/A'} 到 {test_df['date'].max().date() if len(test_df) > 0 else 'N/A'})")
                
                # 验证分割逻辑
                total_split_days = len(train_df) + len(val_df) + len(test_df)
                print(f"分割总天数: {total_split_days} (原始: {len(df)})")
                
                # 计算实际年数
                if len(val_df) > 0 and len(test_df) > 0:
                    val_years = len(val_df) / 365
                    test_years_actual = len(test_df) / 365
                    print(f"实际年数: 验证集 {val_years:.1f}年, 测试集 {test_years_actual:.1f}年")
                
                # 检查时间连续性
                if len(train_df) > 0 and len(val_df) > 0:
                    train_to_val_gap = (val_df['date'].min() - train_df['date'].max()).days
                    print(f"训练→验证间隔: {train_to_val_gap} 天")
                
                if len(val_df) > 0 and len(test_df) > 0:
                    val_to_test_gap = (test_df['date'].min() - val_df['date'].max()).days
                    print(f"验证→测试间隔: {val_to_test_gap} 天")
                
                # 验证逻辑正确性
                expected_test_days = 365
                expected_val_days = 365 if test_years <= 2 else 365 * (test_years - 1)
                
                if abs(len(test_df) - expected_test_days) <= 1:  # 允许1天误差
                    print(f"✅ 测试集天数正确")
                else:
                    print(f"❌ 测试集天数错误: 期望{expected_test_days}, 实际{len(test_df)}")
                
                if abs(len(val_df) - expected_val_days) <= 1:  # 允许1天误差
                    print(f"✅ 验证集天数正确")
                else:
                    print(f"❌ 验证集天数错误: 期望{expected_val_days}, 实际{len(val_df)}")
                
            except Exception as e:
                print(f"❌ 分割失败: {e}")
    
    def test_edge_cases(self):
        """测试边界情况"""
        print(f"\n\n=== 测试边界情况 ===")
        
        # 测试数据不足的情况
        small_df = self.create_test_data(300)  # 不到1年的数据
        print(f"小数据集: {len(small_df)} 天")
        
        try:
            train_df, val_df, test_df = self.split_data_by_time(small_df, 2)
            print(f"分割结果: 训练{len(train_df)}, 验证{len(val_df)}, 测试{len(test_df)}")
            
            if len(train_df) == 0:
                print("⚠️ 训练集为空，数据不足")
            if len(val_df) < 100:
                print("⚠️ 验证集数据较少")
            if len(test_df) < 100:
                print("⚠️ 测试集数据较少")
                
        except Exception as e:
            print(f"❌ 小数据集分割失败: {e}")
    
    def test_parameter_consistency(self):
        """测试参数一致性"""
        print(f"\n\n=== 测试参数一致性 ===")
        
        df = self.create_test_data(1200)
        
        # 测试默认的test_years=2
        print("默认 test_years=2 的分割:")
        train_df, val_df, test_df = self.split_data_by_time(df, 2)
        
        print(f"训练集: {len(train_df)} 天")
        print(f"验证集: {len(val_df)} 天 (期望约365天)")
        print(f"测试集: {len(test_df)} 天 (期望约365天)")
        
        # 验证总的test_years覆盖
        total_test_val_days = len(val_df) + len(test_df)
        expected_total_days = 365 * 2
        
        print(f"验证+测试总天数: {total_test_val_days} (期望约{expected_total_days})")
        
        if abs(total_test_val_days - expected_total_days) <= 10:  # 允许10天误差
            print("✅ test_years=2 的总天数正确")
        else:
            print("❌ test_years=2 的总天数不正确")


def main():
    """运行所有测试"""
    print("开始测试 test_years 参数的使用...")
    
    validator = TestYearsValidator()
    
    # 测试不同的test_years值
    validator.test_different_test_years()
    
    # 测试边界情况
    validator.test_edge_cases()
    
    # 测试参数一致性
    validator.test_parameter_consistency()
    
    print(f"\n🎯 测试完成！")
    print(f"修复后的 split_data_by_time 方法正确使用了 self.test_years 参数")


if __name__ == "__main__":
    main()
